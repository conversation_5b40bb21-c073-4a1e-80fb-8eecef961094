"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { NETWORKS } from "@/utils/contants";
import Storage from "@/libs/storage";

interface NetworkContextType {
  currentNetwork: string;
  setCurrentNetwork: (network: string) => void;
  isNetworkLoaded: boolean;
}

const NetworkContext = createContext<NetworkContextType>({
  currentNetwork: NETWORKS.SUI,
  setCurrentNetwork: () => {},
  isNetworkLoaded: false,
});

export const useNetwork = () => {
  const context = useContext(NetworkContext);
  if (!context) {
    throw new Error("useNetwork must be used within a NetworkProvider");
  }
  return context;
};

interface NetworkProviderProps {
  children: ReactNode;
}

export const NetworkProvider: React.FC<NetworkProviderProps> = ({
  children,
}) => {
  const [currentNetwork, setCurrentNetworkState] = useState<string>(
    NETWORKS.SUI
  );
  const [isNetworkLoaded, setIsNetworkLoaded] = useState(false);

  // Initialize network from localStorage
  useEffect(() => {
    const savedNetwork = Storage.getNetwork();
    setCurrentNetworkState(savedNetwork);
    setIsNetworkLoaded(true);
  }, []);

  const setCurrentNetwork = (network: string) => {
    setCurrentNetworkState(network);
    Storage.setNetwork(network);
  };

  const value: NetworkContextType = {
    currentNetwork,
    setCurrentNetwork,
    isNetworkLoaded,
  };

  return (
    <NetworkContext.Provider value={value}>{children}</NetworkContext.Provider>
  );
};
