"use client";
import * as React from "react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { FireIcon, TopHoldersIcon } from "@/assets/icons";
import { PairTrendingItem } from "@/components";
import { AppDataTableRealtime } from "@/components/AppDataTableRealtime";
import {
  BoxQuickBuy,
  ButtonFilterPair,
  MyPositions,
  PairListType,
} from "@/components/ListPair";
import useWindowSize from "@/hooks/useWindowSize";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import {
  SOCKETS_ROOMS,
  subscribeSocketRoom,
  unsubscribeSocketRoom,
} from "@/libs/socket";
import Storage from "@/libs/storage";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TPair } from "@/types";
import { filterParams, isIOSMobile } from "@/utils/helper";
import {
  buildTemplateUpdateTokenSocialToPairTable,
  buildTemplateUpdatePairStatsToPairTable,
  buildTemplateUpdateTokenAuditToPairTable,
  filterAuditChecked,
  overridePairStatIfNeed,
  overrideDataTableFilter,
} from "@/utils/pair";
import { AppTabResolution } from "@/components";
import { TableTrendingPair } from "@/components/Table/TableTrendingPair";
import Link from "next/link";
import { NETWORKS } from "@/utils/contants";
import { PAIR_FILTER_STORAGE_KEY } from "@/constants";
const RESOLUTIONS = ["5m", "1h", "6h", "24h"];
const LIMIT_PER_PAGE = 100;

export default function TrendingPage() {
  const [resolution, setResolution] = useState<string>(
    Storage.getTrendingRankBy()
  );
  const [buyAmount, setBuyAmount] = useState<any>("1"); // default buy amount
  const [params, setParams] = useState<any>(
    Storage.getPairSearch(PAIR_FILTER_STORAGE_KEY)
  );

  const dataTableRef = useRef<HTMLDivElement>(null);
  const isTablet = useMediaQuery({ query: "(max-width: 992px)" });

  const getTrendingPairs = async (dataTableFilter: any) => {
    const res = await rf
      .getRequest("PairRequest")
      .getPairTrending(NETWORKS.SUI, {
        ...overrideDataTableFilter(dataTableFilter),
        limit: LIMIT_PER_PAGE,
      });

    let newData = res || [];
    newData = newData.map((item: TPair) => {
      return {
        ...item,
        stats: overridePairStatIfNeed(item.stats),
        priceUsd: item.tokenBase.priceUsd,
        priceSui: item.tokenBase.price,
      };
    });

    newData = filterAuditChecked(newData, dataTableFilter);

    newData?.forEach((item: TPair) => {
      subscribeSocketRoom(
        NETWORKS.SUI,
        SOCKETS_ROOMS.PAIR_DETAIL(NETWORKS.SUI, item.pairId)
      );
    });

    return {
      data: newData,
    };
  };

  const handleWhenRefreshData = (event: TBroadcastEvent) => {
    (dataTableRef.current as any)?.refresh();
  };

  useEffect(() => {
    if (!dataTableRef.current) {
      return;
    }
    Storage.setPairSearch(PAIR_FILTER_STORAGE_KEY, params);
    const dataTableFilter = {
      ...filterParams(params),
      resolution,
      network: NETWORKS.SUI,
    };
    (dataTableRef.current as any).filter(dataTableFilter);

    const intervalTrending = setInterval(() => {
      (dataTableRef.current as any)?.polling(dataTableFilter).then();
    }, 30000);

    return () => clearInterval(intervalTrending);
  }, [resolution, params]);

  const handleWhenSocketConnected = useCallback(() => {
    const items = (dataTableRef.current as any)?.getItems();
    items?.forEach((item: TPair) => {
      subscribeSocketRoom(
        NETWORKS.SUI,
        SOCKETS_ROOMS.PAIR_DETAIL(NETWORKS.SUI, item.pairId)
      );
    });
  }, []);
  useEffect(() => {
    AppBroadcast.on(
      BROADCAST_EVENTS.SOCKET_CONNECTED,
      handleWhenSocketConnected
    );
    return () => {
      const items = (dataTableRef.current as any)?.getItems();
      items?.forEach((item: TPair) => {
        unsubscribeSocketRoom(
          NETWORKS.SUI,
          SOCKETS_ROOMS.PAIR_DETAIL(NETWORKS.SUI, item.pairId)
        );
      });
      AppBroadcast.remove(
        BROADCAST_EVENTS.SOCKET_CONNECTED,
        handleWhenSocketConnected
      );
    };
  }, []);

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    };
  }, []);

  const selectResolution = (selectedResolution: string) => {
    setResolution(selectedResolution);
    Storage.setTrendingRankBy(selectedResolution);
  };

  const TableRow = ({ item, ...restProps }: any) => {
    return (
      <tr
        className={`${
          isTablet ? `` : `hover:bg-neutral-alpha-50 cursor-pointer`
        } border-white-50 group border-b `}
        {...restProps}
      />
    );
  };

  const Table = ({ ...restProps }: any) => {
    return <table className="w-full" {...restProps} />;
  };

  const TableComponents = {
    TableRow,
    Table,
  };

  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
  const hasPosition = useSelector(
    (state: RootState) => state.user.positions?.length > 0
  );
  const isHideInstallApp = useSelector(
    (state: RootState) => state.metadata.isHideInstallApp
  );

  const { windowHeight } = useWindowSize();

  const tableHeight = useMemo(() => {
    if (isMobile) {
      // For IOS
      if (isIOSMobile()) {
        if (!isHideInstallApp) {
          return `calc(${windowHeight}px - 210px - 40px)`;
        }
        return `calc(${windowHeight}px - 210px)`;
      }

      // For android
      if (!isHideInstallApp) {
        return `calc(${windowHeight}px - 195px - 40px)`;
      }
      return `calc(${windowHeight}px - 195px)`;
    }

    return hasPosition
      ? `calc(${windowHeight}px - 173px)`
      : `calc(${windowHeight}px - 141px)`;
  }, [isMobile, windowHeight, hasPosition]);

  if (isMobile) {
    return (
      <>
        <div className="mx-[8px]">
          <PairListType className="mt-[12px]" />
          <div className="my-[8px] flex items-center justify-between gap-[8px] md:w-auto">
            <ButtonFilterPair
              params={params}
              setParams={setParams}
              height={32}
              showText={false}
            />
            <div className="flex items-center gap-2">
              <AppTabResolution
                value={resolution}
                onChange={selectResolution}
                options={RESOLUTIONS.map((item) => ({
                  name: item,
                  value: item,
                }))}
              />
              <BoxQuickBuy buyAmount={buyAmount} setBuyAmount={setBuyAmount} />
            </div>
          </div>
        </div>
        <TableTrendingPair
          resolution={resolution}
          params={params}
          buyAmount={buyAmount}
          tableHeight={tableHeight}
        />
      </>
    );
  }

  return (
    <>
      <MyPositions />
      <div className="tablet:flex-row mx-[8px] mb-[16px] flex flex-col justify-between gap-4 pt-[12px] md:mx-[20px] md:mb-[24px]">
        <div className="border-white-50 flex w-full items-center gap-[20px] border-b pb-2 md:w-1/2 lg:pb-0">
          <Link href={"/new-pairs"}>
            <div className="text-white-800 mb-[-8px] cursor-pointer text-[14px] font-medium leading-[1.2] md:text-[18px]">
              New Pairs
            </div>
          </Link>
          <Link href={"/funzone"}>
            <div className="text-white-800 mb-[-8px] cursor-pointer text-[14px] font-medium leading-[1.2] md:text-[18px]">
              Fun Zone
            </div>
          </Link>
          <div className="text-brand-500 mb-[-6px] flex flex-col items-center text-[18px] font-semibold leading-[1.2] md:mb-[-8px] md:text-[24px]">
            Trending
            <div className="bg-brand-500 h-[2px] w-[24px] rounded-[100px]"></div>
          </div>
        </div>
        <div className="max-tablet:justify-between flex flex-row justify-end gap-[8px]">
          <div className="md:bg-white-100 bg-white-50 body-sm-regular-12 text-white-500 flex items-center gap-[4px] rounded-[6px] p-0 md:px-[8px] md:py-[4px]">
            <TopHoldersIcon className="hidden md:block" />
            <div className="hidden min-w-[50px] md:block">Rank by</div>
            <div className="bg-black-500 flex flex-1 gap-[4px] rounded-[4px] p-[4px]">
              {RESOLUTIONS.map((r, index) => {
                return (
                  <div
                    key={index}
                    className={`hover:text-neutral-0 body-sm-medium-12 flex-1 cursor-pointer rounded-[4px] px-[8px] py-[4px] text-center  ${
                      r === resolution
                        ? "bg-white-100 text-neutral-0"
                        : "text-white-500"
                    }`}
                    onClick={() => selectResolution(r)}
                  >
                    {r}
                  </div>
                );
              })}
            </div>
          </div>
          <div className="flex items-center justify-between gap-[8px]">
            <ButtonFilterPair params={params} setParams={setParams} />
            <BoxQuickBuy buyAmount={buyAmount} setBuyAmount={setBuyAmount} />
          </div>
        </div>
      </div>

      <AppDataTableRealtime
        minWidth={1440}
        ref={dataTableRef}
        shouldAutoFetchOnInit={false}
        limit={LIMIT_PER_PAGE}
        height={tableHeight}
        overrideHeaderClassName={`min-w-[1440px] px-[12px] flex body-sm-regular-12 text-white-500 border-y border-white-50 bg-white-50`}
        overrideBodyClassName="w-full hide-scroll"
        getData={getTrendingPairs}
        handleUpdateItem={[
          buildTemplateUpdatePairStatsToPairTable(),
          buildTemplateUpdateTokenAuditToPairTable(),
          buildTemplateUpdateTokenSocialToPairTable(),
        ]}
        renderHeader={() => {
          if (isTablet) return <></>;
          return (
            <>
              <tr className="body-sm-regular-12 text-white-500 h-[31px] bg-[#13141a]">
                <th className="md:min-[32px] sticky left-0 w-[3%] min-w-[20px] bg-[#13141a] px-[4px] py-[6px] md:px-[8px] lg:static lg:bg-transparent">
                  <div className="flex h-full w-full items-center justify-center">
                    <FireIcon />
                  </div>
                </th>
                <th className="md:min-[32px] sticky left-[28px] w-[3%] min-w-[20px] bg-[#13141a] px-[4px] py-[6px] md:left-[34px] md:px-[8px] lg:static lg:bg-transparent" />
                <th className="sticky left-[54px] w-[13%]  min-w-[124px] bg-[#13141a] md:left-[68px] md:min-w-[200px] lg:static lg:bg-transparent">
                  <div className="border-white-50 border-r px-[8px] py-[6px] text-left md:border-0">
                    Token
                  </div>
                </th>
                <th className="w-[6%] min-w-[67px] px-[8px] py-[6px] text-center md:min-w-[80px]">
                  Created
                </th>
                <th className="w-[7%] min-w-[92px] px-[8px] py-[6px] text-right md:min-w-[132px]">
                  Liquidity
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  MC
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  Holders
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  Tnxs
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  Vol.
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  5m
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  1h
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  6h
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  24h
                </th>
                <th className="w-[7%] min-w-[90px] px-[8px] py-[6px] text-end md:min-w-[105px]">
                  DEV
                </th>
                <th className={`w-[8%] min-w-[105px] px-[8px] py-[6px]`}>
                  Audit check
                </th>
                <th className="sticky right-0 w-[11%] min-w-[78px] bg-[#13141a] text-center md:min-w-[148px] lg:static lg:bg-transparent">
                  <div className="border-white-50 w-full border-l px-[8px] py-[6px] text-center md:border-0">
                    Action
                  </div>
                </th>
              </tr>
            </>
          );
        }}
        renderRow={(item: TPair, index: number) => {
          if (isTablet) {
            return (
              <>
                <div className="mx-2 mb-2 flex flex-col gap-2">
                  <PairTrendingItem
                    item={item}
                    index={index}
                    selectedResolution={resolution}
                    buyAmount={buyAmount}
                    isItemInBasicTable
                    filterParams={params}
                  />
                </div>
              </>
            );
          }
          return (
            <>
              <PairTrendingItem
                item={item}
                index={index}
                selectedResolution={resolution}
                buyAmount={buyAmount}
                isItemInBasicTable
                filterParams={params}
              />
            </>
          );
        }}
        isHideHeader={isTablet}
        isBasicTable={!isTablet}
        components={TableComponents}
      />
    </>
  );
}
